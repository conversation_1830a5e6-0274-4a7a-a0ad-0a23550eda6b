package com.yf.exam.modules.station.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
* <p>
* 气象站点区域代码查询请求类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@ApiModel(value="气象站点区域代码查询请求", description="气象站点区域代码查询请求")
public class ElStationRegionReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "区域编号数组（1-9）", required = true, example = "[\"1\", \"2\", \"9\"]",
                      notes = "1-华北区域，2-东北区域，3-长江中下游区域，4-华南区域，5-西南地区东部，6-青藏高原区域，7-新疆区域，8-西北地区东部，9-内蒙古区域")
    @NotEmpty(message = "区域编号数组不能为空")
    private String regionCode;

    @ApiModelProperty(value = "站点等级")
    private String stationLevl;

    @ApiModelProperty(value = "是否在线")
    private Integer online;

    @ApiModelProperty(value = "是否绘制到地图")
    private Integer drawTown;
}
