package com.yf.exam.modules.weather.micaps;

import java.util.List;

/**
 * MICAPS第四类数据：格点数据
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
public class MicapsType4Data extends MicapsData {
    
    /** 时效（预报小时数） */
    private int forecastHour;
    
    /** 层次 */
    private int level;
    
    /** 经度格距 */
    private double lonInterval;
    
    /** 纬度格距 */
    private double latInterval;
    
    /** 起始经度 */
    private double startLon;
    
    /** 终止经度 */
    private double endLon;
    
    /** 起始纬度 */
    private double startLat;
    
    /** 终止纬度 */
    private double endLat;
    
    /** 纬向格点数 */
    private int latGridNum;
    
    /** 经向格点数 */
    private int lonGridNum;
    
    /** 等值线间隔 */
    private double contourInterval;
    
    /** 等值线起始值 */
    private double contourStartValue;
    
    /** 等值线终止值 */
    private double contourEndValue;
    
    /** 平滑系数 */
    private double smoothFactor;
    
    /** 加粗线值 */
    private double boldLineValue;
    
    /** 格点数据值列表（按先纬向后经向排列） */
    private List<Double> gridValues;
    
    // Getters and Setters
    
    public int getForecastHour() {
        return forecastHour;
    }
    
    public void setForecastHour(int forecastHour) {
        this.forecastHour = forecastHour;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public double getLonInterval() {
        return lonInterval;
    }
    
    public void setLonInterval(double lonInterval) {
        this.lonInterval = lonInterval;
    }
    
    public double getLatInterval() {
        return latInterval;
    }
    
    public void setLatInterval(double latInterval) {
        this.latInterval = latInterval;
    }
    
    public double getStartLon() {
        return startLon;
    }
    
    public void setStartLon(double startLon) {
        this.startLon = startLon;
    }
    
    public double getEndLon() {
        return endLon;
    }
    
    public void setEndLon(double endLon) {
        this.endLon = endLon;
    }
    
    public double getStartLat() {
        return startLat;
    }
    
    public void setStartLat(double startLat) {
        this.startLat = startLat;
    }
    
    public double getEndLat() {
        return endLat;
    }
    
    public void setEndLat(double endLat) {
        this.endLat = endLat;
    }
    
    public int getLatGridNum() {
        return latGridNum;
    }
    
    public void setLatGridNum(int latGridNum) {
        this.latGridNum = latGridNum;
    }
    
    public int getLonGridNum() {
        return lonGridNum;
    }
    
    public void setLonGridNum(int lonGridNum) {
        this.lonGridNum = lonGridNum;
    }
    
    public double getContourInterval() {
        return contourInterval;
    }
    
    public void setContourInterval(double contourInterval) {
        this.contourInterval = contourInterval;
    }
    
    public double getContourStartValue() {
        return contourStartValue;
    }
    
    public void setContourStartValue(double contourStartValue) {
        this.contourStartValue = contourStartValue;
    }
    
    public double getContourEndValue() {
        return contourEndValue;
    }
    
    public void setContourEndValue(double contourEndValue) {
        this.contourEndValue = contourEndValue;
    }
    
    public double getSmoothFactor() {
        return smoothFactor;
    }
    
    public void setSmoothFactor(double smoothFactor) {
        this.smoothFactor = smoothFactor;
    }
    
    public double getBoldLineValue() {
        return boldLineValue;
    }
    
    public void setBoldLineValue(double boldLineValue) {
        this.boldLineValue = boldLineValue;
    }
    
    public List<Double> getGridValues() {
        return gridValues;
    }
    
    public void setGridValues(List<Double> gridValues) {
        this.gridValues = gridValues;
    }
    
    /**
     * 获取指定经纬度位置的格点值（双线性插值）
     * 
     * @param lon 经度
     * @param lat 纬度
     * @return 插值后的格点值，如果超出范围返回null
     */
    public Double getValueAtPosition(double lon, double lat) {
        if (gridValues == null || gridValues.isEmpty()) {
            return null;
        }
        
        // 检查是否在数据范围内
        if (lon < startLon || lon > endLon || lat < startLat || lat > endLat) {
            return null;
        }
        
        // 计算格点索引
        double lonIndex = (lon - startLon) / lonInterval;
        double latIndex = (lat - startLat) / latInterval;
        
        int lonIdx = (int) Math.floor(lonIndex);
        int latIdx = (int) Math.floor(latIndex);
        
        // 边界检查
        if (lonIdx < 0 || lonIdx >= lonGridNum - 1 || latIdx < 0 || latIdx >= latGridNum - 1) {
            // 如果在边界上，返回最近的格点值
            lonIdx = Math.max(0, Math.min(lonGridNum - 1, (int) Math.round(lonIndex)));
            latIdx = Math.max(0, Math.min(latGridNum - 1, (int) Math.round(latIndex)));
            return getGridValue(latIdx, lonIdx);
        }
        
        // 双线性插值
        double dx = lonIndex - lonIdx;
        double dy = latIndex - latIdx;
        
        Double v00 = getGridValue(latIdx, lonIdx);
        Double v10 = getGridValue(latIdx, lonIdx + 1);
        Double v01 = getGridValue(latIdx + 1, lonIdx);
        Double v11 = getGridValue(latIdx + 1, lonIdx + 1);
        
        if (v00 == null || v10 == null || v01 == null || v11 == null) {
            return null;
        }
        
        double v0 = v00 * (1 - dx) + v10 * dx;
        double v1 = v01 * (1 - dx) + v11 * dx;
        
        return v0 * (1 - dy) + v1 * dy;
    }
    
    /**
     * 获取指定格点位置的值
     * 
     * @param latIdx 纬度索引
     * @param lonIdx 经度索引
     * @return 格点值
     */
    public Double getGridValue(int latIdx, int lonIdx) {
        if (gridValues == null || latIdx < 0 || latIdx >= latGridNum || 
            lonIdx < 0 || lonIdx >= lonGridNum) {
            return null;
        }
        
        // 按先纬向后经向的顺序存储
        int index = latIdx * lonGridNum + lonIdx;
        if (index >= gridValues.size()) {
            return null;
        }
        
        return gridValues.get(index);
    }
    
    @Override
    public String toString() {
        return "MicapsType4Data{" +
                "dataType=" + getDataType() +
                ", description='" + getDescription() + '\'' +
                ", year=" + getYear() +
                ", month=" + getMonth() +
                ", day=" + getDay() +
                ", hour=" + getHour() +
                ", forecastHour=" + forecastHour +
                ", level=" + level +
                ", lonInterval=" + lonInterval +
                ", latInterval=" + latInterval +
                ", startLon=" + startLon +
                ", endLon=" + endLon +
                ", startLat=" + startLat +
                ", endLat=" + endLat +
                ", latGridNum=" + latGridNum +
                ", lonGridNum=" + lonGridNum +
                ", gridPointsCount=" + (gridValues != null ? gridValues.size() : 0) +
                '}';
    }
}
